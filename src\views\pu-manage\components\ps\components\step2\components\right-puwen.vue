<template>
  <div class="right-pu-wen-wrap">
    <LabelItem title="选择家族" width="100" class="mt4 mb8">
      <a-select style="width: 100%" v-model="selectedClanId" placeholder="选择家族" @change="handleSelectedClan">
        <a-select-option :key="clan.id" v-for="clan in clanList" :value="clan.id">{{ clan.name }}</a-select-option>
      </a-select>
    </LabelItem>
    <div class="catalog-li">
      <a-spin :spinning="groupListLoading">
        <div v-for="item in groupList" :key="item.id" class="catalog-item">
          <div class="title">
            <div>
              <i
                class="cus-edit-toolbar-icon"
                :class="openCategoryIdList.includes(item.id) ? 'icon-edit-toolbardakai':'icon-edit-toolbarwenjianjia'"
                style="font-size:16px;margin:0"
              />
              {{ item.name }}
            </div>
            <div></div>
            <div class="article-item" v-show="openCategoryIdList.includes(item.id)">
              <div
                v-for="article in item.article_info"
                :key="article.id"
                class="c-pointer-hover mt4 flex justify-content-space-between"
                :class="selectedArticleIdList.includes(article.id)?'selected': ''"
                @click="(e) => handleArticleClick(article, item, e)"
              >
                <div><i class="cus-edit-toolbar-icon icon-edit-toolbarguangbiaodaohang mr4 guanbiao"/> {{ article.name }}</div>
                <div><i class="cus-edit-toolbar-icon icon-edit-toolbarduigou" v-if="selectedArticleIdList.includes(article.id)"/> </div>
              </div>
            </div>
          </div>
        </div>
        <a-empty v-show="groupList?.length===0" />
      </a-spin>
    </div>
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import LabelItem from '@/components/LabelItem'
import { getSpecificLevelIdList } from '@/utils/getLevelIdsUtls'
export default {
  name: 'RightPuWen',
  components: { LabelItem },
  data () {
    return {
      clanList: [],
      groupList: [],
      selectedClanId: '',
      openCategoryIdList: [],
      selectedArticleIdList: [],
      selectedArticleList: [],
      groupListLoading: false
    }
  },
  computed: {
    ...mapState({
      groupClan: state => state.family.groupClan,
      currentSelectedClanId: state => state.family.currentSelectedClanId
    })
  },
  watch: {
    groupClan: {
      handler (val) {
        let list = []
        if (val?.length > 0) {
          (val || []).forEach(item => {
            if (item.clan_list?.length > 0) {
              list = list.concat(item.clan_list)
            }
          })
        }
        this.clanList = list
      },
      deep: true,
      immediate: true
    },
    currentSelectedClanId: {
      handler (val) {
        this.selectedClanId = val
      },
      deep: true,
      immediate: true
    },
    selectedClanId: {
      handler (val) {
        this.getList()
      }
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    ...mapActions(['GetClanArticleList', 'GetCategoryList', 'AddClanArticle']),
    handleSelectedClan (id) {
      this.selectedClanId = id
    },
    handleArticleClick (article, category, e) {
      e.stopPropagation()
      this.selectedArticleIdList = [article.id]
      this.selectedArticleList = [article]
    },
    async getList () {
      const { GetClanArticleList, selectedClanId } = this
      if (!selectedClanId) {
        return
      }
      this.groupListLoading = true
      const res = await GetClanArticleList({ clan_id: selectedClanId })
      this.groupListLoading = false
      if (res.code === 200) {
        const _data = res.data.list
        const idList = getSpecificLevelIdList(_data)
        this.openCategoryIdList = idList
        this.groupList = _data
      }
    }
  }
}
</script>
<style lang="less" scoped>
.right-pu-wen-wrap{
  height: 100%;
  padding: 10px;
  background-color: #fff;
  .catalog-li {
    display:flex;
    flex-direction:column;
    width:100%;
    font-size:12px;
    .catalog-item {
      .title {
        padding: 0 4px;
        cursor:pointer;
        font-size:16px;
        display:flex;
        flex-direction:column;
        .dynamic-icon {
          display:none;
        }
      }
      .title:hover {
        .dynamic-icon {
          display:inline-block;
        }
      }
      .title.selected { color: #ff7926;}
      .article-item {
        font-size: 14px;
        padding: 6px 4px 6px 20px;
        background-color:#fff;
        .selected { color: #ff7926;}
      }
    }
  }
}
</style>
