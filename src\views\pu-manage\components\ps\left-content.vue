<template>
  <div :class="toggleClass()">
    <div title="收起" class="toggle-btn" @click="handleToggle">
      <i>{{ visible ? '<' : '>' }}</i>
      <div class="hint">
        <div>点</div>
        <div>击</div>
        <div>{{ visible ? '收' : '展' }}</div>
        <div>{{ visible ? '起' : '开' }}</div>
      </div>
    </div>
    <div v-if="visible">
      <div class="operate">
        <i class="cus-edit-toolbar-icon icon-edit-toolbarzengjiawenzhang" title="新建谱书" @click="handleAddBook()" />
        <i class="cus-edit-toolbar-icon icon-edit-toolbarkongxinjiantou1" title="下移" @click="handleMove('up')"/>
        <i class="cus-edit-toolbar-icon icon-edit-toolbarkongxinjiantou" title="上移"@click="handleMove('up')"/>
        <i class="cus-edit-toolbar-icon icon-edit-toolbarfuzhi" title="复制" @click="handleCopy()"/>
        <i class="cus-edit-toolbar-icon icon-edit-toolbarshanchu1" title="删除" @click="handleTopDelete()"/>
      </div>
      <div style="padding: 4px 5px">
        <a-input-search placeholder="输入要查询的内容" enter-button/>
      </div>
      <a-divider style="margin: 12px 0"/>
      <a-spin :spinning="groupListLoading">
        <div class="catalog-li">
          <div v-for="item in booksList" :key="item.id" class="catalog-item">
            <div class="item" :class="selectedBookId === item.id?' selected':''" @click="handleSelectedBook(item)">
              <div class="cover-container" style="transform: scale(0.78);">
                <img src="../../../../assets/cover-list2.png" alt="" class="cover" />
                <div class="title">
                  <div class="title2">
                    <div class="title3">
                      <div class="title4" style="transform: scale(0.56);">{{ item.plan_name || '' }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex-1" style="text-align:left">
                <div class="f18">{{ item.plan_name }}</div>
                <div class="f14">{{ item.volume }}</div>
                <div class="f14">{{ item.hall }}</div>
                <div class="edit"><i class="cus-edit-toolbar-icon icon-edit-toolbarxiugai" @click="handleEditBook(item)"/></div>
              </div>
            </div>
          </div>
          <a-empty v-show="booksList?.length===0" />
        </div>
      </a-spin>
      <div class="add-category" @click="handleAddBook()">
        <i class="cus-edit-toolbar-icon icon-edit-toolbarzengjiawenjianjia" />新建谱书
      </div>
    </div>
    <add-book
      v-if="addBookModal"
      :item="selectedBook"
      :id="selectedBookId"
      @close="handleCloseAddBook"
      @refresh="getList"/>
    <add-category v-if="addCategoryModal" @close="handleCloseAddBook" @refresh="getList" />
    <add-article v-if="addArticleModal" :category="selectedAddArticleCategory" @close="addArticleModal=false" @refresh="getList"/>
    <family-info-component v-if="showFamilyBaseInfo" @close="showFamilyBaseInfo=false"/>
  </div>
</template>
<script>
import FamilyInfoComponent from '@/views/components/family-info'
import AddBook from '@/views/pu-manage/components/ps/components/add-book'
import AddArticle from '@/views/pu-manage/components/pw/components/add-article'
import AddCategory from '@/views/pu-manage/components/pw/components/add-category'
import { Modal } from 'ant-design-vue'
import { mapActions, mapState } from 'vuex'
export default {
  name: 'LeftContentForPS',
  components: { AddBook, FamilyInfoComponent, AddCategory, AddArticle },
  data () {
    return {
      visible: true,
      addBookModal: false,
      showFamilyBaseInfo: false,
      addCategoryModal: false,
      addArticleModal: false,
      selectedAddArticleCategory: null,
      openCategoryIdList: [],
      selectedBookId: -1,
      selectedBook: null,
      bookDetailLoading: false,
      selectedArticleId: '',
      groupListLoading: true,
      groupList: [],
      booksList: [],
      editItem: null,
      clan: {}
    }
  },
  computed: {
    ...mapState({
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedGroupId: state => state.family.currentSelectedGroupId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    })
  },
  async mounted () {
    await this.getList()
    if (!this.currentSelectedGroupId) {
      return
    }
    const result = await this.GetCurrentClan()
    this.clan = result
  },
  methods: {
    ...mapActions(['GetClanBookPrintPlan', 'DeleteClanBook', 'UpdateSelectedBook', 'GetBookDetail', 'UpdateCategory', 'GetCurrentClan', 'UpdateClanArticle']),
    handleSelectedBook (item) {
      this.selectedBookId = item.id
      this.getDetail()
      this.UpdateSelectedBook(item)
    },
    handleEditBook (item) {
      this.selectedBookId = item.id
      this.selectedBook = item
      this.addBookModal = true
    },
    handleArticleClick (article, category) {
      this.selectedCategoryId = category.id
      this.selectedArticleId = article.id
      this.$emit('update', { article, category })
    },
    handleAddBook () {
      this.addBookModal = true
    },
    handleCloseAddBook () {
      this.addBookModal = false
      this.selectedBook = null
    },
    async handleMove (type) {
      this.$message.info('移动信息待完善')
      switch (type) {
        case 'up':
          break
        case 'down':
          break
      }
    },
    async handleCopy () {
      this.$message.info('该功能完善中')
      this.loading = true
      // 进行copy逻辑
      this.loading = false
    },
    async handleTopDelete () {
      if (!this.selectedBookId) {
        this.$message.info('请选择要删除的谱书')
        return
      }
      const { DeleteClanBook, currentSelectedClanId, selectedBookId } = this
      if (this.loading) {
        return
      }
      const that = this
      const obj = this.booksList.find(item => item.id === this.selectedBookId)
      const bookName = obj ? obj.plan_name : ''
      this.$confirm({
        title: '',
        content: `确定要删除「${bookName}」谱书吗？`,
        okText: '确定',
        okType: 'danger',
        cancelText: '取消',
        async onOk () {
          that.loading = true
          const res = await DeleteClanBook({ clan_id: currentSelectedClanId, book_id: selectedBookId })
          that.loading = false
          if (res && res.code === 200) {
            that.$message.success('删除成功')
            await that.getList()
          } else {
            that.$message.error(res.message || '删除失败')
          }
        }
      })
    },
    handleQianYi () {
      if (!this.currentSelectedPedigreeId) {
        this.$message.info('请选择要迁移的文章')
        return
      }
      this.loading = true
      // 进行迁移逻辑
      this.loading = false
    },
    handleEdit (item, e) {
      e.stopPropagation()
      const { UpdateCategory } = this
      const that = this
      Modal.confirm({
        title: '修改谱文类目',
        icon: null,
        content: <a-input ref='editInput' id='editInput' />,
        async onOk () {
          const name = document.getElementById('editInput').value.trim()
          if (!name) {
            that.$message.error('请输入类目名称')
            return
          }
          const res = await UpdateCategory({
            category_id: item.id,
            name: name,
            clan_id: that.currentSelectedClanId
          })
          if (res && res.code === 200) {
            that.$message.success('修改成功')
            await that.getList()
          } else {
            that.$message.error(res.message || '修改失败')
          }
        },
        onCancel () {
          console.log('Cancel')
        }
      })
      this.$nextTick(() => {
        this.$refs.editInput.value = item.name
      })
    },
    async handleDelete (item, e) {
      e.stopPropagation()
    },
    async remove (id) {
      const { DeletedCategory, currentSelectedClanId } = this
      const res = await DeletedCategory({ category_id: id, clan_id: currentSelectedClanId })
      if (res && res.code === 200) {
        this.$message.success('删除成功')
        await this.getList()
      } else {
        this.$message.error(res.message || '删除失败')
      }
    },
    async handleAddArticle (category) {
      if (!this.currentSelectedPedigreeId) {
        this.$message.info('请先添加家族')
        return
      }
      this.selectedAddArticleCategory = category
      this.addArticleModal = true
    },
    async removeArticle (id, e) {
      e.stopPropagation()
      console.log('id', id)
    },
    async handleArticleEdit (article, e) {
      e.stopPropagation()
      const { UpdateClanArticle } = this
      const that = this
      Modal.confirm({
        title: '修改谱文标题',
        icon: null,
        content: <a-input ref='editInput' id='articleInput' />,
        async onOk () {
          const name = document.getElementById('articleInput').value.trim()
          if (!name) {
            that.$message.error('请输入谱文标题')
            return
          }
          const res = await UpdateClanArticle({
            id: article.id,
            name: name,
            clan_id: that.currentSelectedClanId
          })
          if (res && res.code === 200) {
            that.$message.success('修改成功')
            await that.getList()
          } else {
            that.$message.error(res.message || '修改失败')
          }
        },
        onCancel () {
          console.log('Cancel')
        }
      })
      this.$nextTick(() => {
        this.$refs.editInput.value = article.name
      })
    },
    async handleDeleteArticle (articleId, e) {
      e.stopPropagation()
      console.log('id', articleId)
      this.$message.info('删除信息待完善')
    },
    async getList () {
      const { GetClanBookPrintPlan, currentSelectedClanId } = this
      if (!currentSelectedClanId) {
        return
      }
      this.groupListLoading = true
      const res = await GetClanBookPrintPlan({ clan_id: currentSelectedClanId })
      this.groupListLoading = false
      if (res.code === 200) {
        const _data = res.data.list
        this.booksList = _data
        this.handleDefaultSelected(_data)
      }
    },
    async getDetail  () {
      const { GetBookDetail, currentSelectedClanId, selectedBookId } = this
      if (!currentSelectedClanId || !selectedBookId) {
        return
      }
      this.bookDetailLoading = true
      await GetBookDetail({ clan_id: currentSelectedClanId, book_id: selectedBookId })
      this.bookDetailLoading = false
    },
    // 处理默认选中
    handleDefaultSelected (data) {
      if (data?.length > 0) {
        const { selectedBookId } = this
        if (selectedBookId < 0) {
          this.selectedBookId = data[0].id
          this.UpdateSelectedBook(data[0])
          this.getDetail()
        } else {
           const obj = data.find(item => item.id === selectedBookId)
          if (!obj) {
            this.selectedBookId = data[0].id
            this.UpdateSelectedBook(data[0])
            this.getDetail()
          }
        }
      }
    },
    handleToggle () {
      this.visible = !this.visible
    },
    toggleClass () {
      return this.visible ? 'left-content' : 'left-content close'
    }
  },
    watch: {
    selectedBookId (val) {
      if (val) {
        this.$emit('onChangeSelectedBookId', val)
      }
    }
  }
}
</script>
<style lang='less' scoped>

.left-content {
  width: 265px;
  margin-top: 6px;
  height: calc(100vh - 70px);
  background-color: #fff;
  position:relative;
  transition: width 300ms ease;
  .toggle-btn{
    position:absolute;
    top:50%;
    transform:translateY(-50%);
    width:18px;
    height:160px;
    display:flex;
    flex-direction:column;
    align-items:center;
    justify-content:center;
    background-color:#ebebeb;
    color:#666;
    cursor:pointer;
    transition:all .2s;
    z-index:10;
    -webkit-user-select:none;
    -moz-user-select:none;
    user-select:none;
    left:100%;
    border-radius:0 100px 100px 0;
    .hint{ display:none;}
    &:hover {
      background-color: #f86e04;
      color: #fff;
      .hint { display: block}
    }
  }

  .demo {
    width: 261px;
    height: 52px;
    background:  #f0f2f5 url(~@/assets/nav_level_3.png) no-repeat 50%;
    background-size: cover;
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 15px;
    padding-right: 10px;
    box-sizing: border-box;
    margin-left: 5px;
    border-radius: 2px;
    overflow: hidden;
    cursor: pointer;
    z-index: 1;
    .tag{
      position: absolute;
      left: 0;
      top: 0;
      width: 80px;
      height: 16px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      z-index: 10;
      transform: rotate(-45deg);
      transform-origin: 42% 267%;
      font-size: 12px
    }
    .tag-fanli {
      background: #b91212;
    }
    .line-word {
      font-size:24px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .custom-collapse {
    border: 0;
    background-color:#fff;
    /deep/ .ant-collapse-item {
      border: 0;
    }
  }
}
.catalog-li {
  display:flex;
  flex-direction:column;
  width:100%;
  font-size:12px;
  .catalog-item {
    .item {
      position: relative;
      margin: 10px 16px;
      border: 1px solid #e5e5e5;
      padding: 4px 8px;
      height: 114px;
      display:flex;
      .edit {
        position:absolute;
        right: 4px;
        bottom: 10px;
        i { font-size: 16px}
      }
    }
    .item.selected {
      border: 1px solid #ff7926;
      background-color:linear-gradient(135deg,#fff7f0,#fff);
    }
  }
}
.add-category {
  margin-top:12px;
  margin-left:8px;
  display:flex;
  height: 110px;
  width: 95%;
  align-items:center;
  justify-content:center;
  border: 1px dashed #f86e04;
  border-radius:2px;
  cursor:pointer;
  color: #ff7926;
  i {
    font-size: 14px;
  }
}
.left-content.close{
  width: 0;
  transition: width 300ms ease;
}
.operate {
  margin: 6px 0;
  display: flex;
  justify-content: space-between;
}
.line {
  display: inline-block;
  width: 4px;
  height: 16px;
  background: #f76d02;
  margin-right: 6px
}
.cus-edit-toolbar-icon {
  font-size: 28px;
  margin: 0 10px;
}
.cus-edit-toolbar-icon:hover {
  color: #f86e04;
  cursor: pointer;
}
.cover-container{
  width: 100px;
  height: 130px;
  transform-origin: left top;
  position: relative;
  .cover{
    display: block;
    width: 100%;
    height: 100%;
    -o-object-fit: fill;
    object-fit: fill;
  }
  .title {
    position: absolute;
    left: 64px;
    top: 16px;
    width: 24px;
    height: 76px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    border: 2px solid #fff;
    background-color: #92958d;
    padding: 1px;
    .title2{
      width: 100%;
      height: 100%;
      border: 1px solid #eae2c9;
      background-color: #dab272;
      box-sizing: border-box;
      padding: 1px;
      .title3{
        width: 100%;
        height: 100%;
        background-color: #fff6c3;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .title4{
          width: 100%;
          writing-mode: vertical-lr;
          white-space: nowrap;
          text-align: center;
          font-size: 12px;
          line-height: 14px;
          box-sizing: border-box;
        }
      }
    }
  }
}
</style>
