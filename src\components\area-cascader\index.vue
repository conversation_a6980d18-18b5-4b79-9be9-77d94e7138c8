<template>
  <a-cascader
    :field-names="{ label: 'name', value: 'name', children: 'child' }"
    :options="options"
    :value="value"
    change-on-select
    placeholder="请选择"
    :load-data="loadData"
    @change="onChange" />
</template>
<script>
// 新增直辖市识别逻辑
import { getAreaList } from '@/api/common'
const DIRECT_CITY_NAMES = ['北京', '天津', '上海', '重庆'] // 北京/天津/上海/重庆

export default {
  data () {
    return {
      options: [],
      value: this.initValue || []
    }
  },
  watch: {
    initValue (val = []) {
      this.value = val
    }
  },
  props: {
    initValue: {
      type: Array,
      default: () => []
    }
  },
  mounted () {
    this.getprovince()
  },
  methods: {
    async getprovince () {
      const { code, data } = await getAreaList({ fid: 4744 })
      if (code === 200) {
        this.options = data.list.map(item => ({
          name: item.name,
          id: item.id,
          isLeaf: false
        })
        ).sort((a, b) => {
          return a.id - b.id
        })
      }
    },
    async loadData (selectedOptions) {
      const targetOption = selectedOptions[selectedOptions.length - 1]
      // 最多允许加载到第三级（0:省份,1:城市,2:区县）
      const currentLevel = selectedOptions.length // 当前层级
      // 识别直辖市特殊处理
      const isDirectCity = DIRECT_CITY_NAMES.includes(targetOption.name)
      targetOption.loading = true
      // 判断
      const { code, data } = await getAreaList({
        fid: targetOption.id // 使用当前选中项的id作为父ID
      })
      if (code === 200) {
        targetOption.child = data.list.map(item => ({
          name: item.name,
          id: item.id,
          isLeaf: isDirectCity ? true : currentLevel >= 2 // 根据实际层级设置
        })).sort((a, b) => {
          return a.id - b.id
        })
      }
      targetOption.loading = false
      this.options = [...this.options]
    },
    onChange (value, selectedOptions) {
      console.log(value, selectedOptions)
      this.value = value
      this.$emit('change', value)
    }
  }
}
</script>
<style scoped lang='less'></style>
