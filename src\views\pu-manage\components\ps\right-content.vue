<template>
  <div class="right-content">
    <template v-if="selectedStep < 0">
      <step-content-for-ps @step="handleStep" />
      <detail-list-content-for-ps />
    </template>
    <template v-else>
      <step1-select-cover v-if="selectedStep === 1" @return="handleReturn" @save="handleSave" />
      <step2-edit-content v-if="selectedStep === 2" @return="handleReturn" @save="handleSave" />
      <step3-print-format v-if="selectedStep === 3" @return="handleReturn" @save="handleSave" />
    </template>
  </div>
</template>
<script>
import Step1SelectCover from '@/views/pu-manage/components/ps/components/step1/step1-select-cover'
import Step2EditContent from '@/views/pu-manage/components/ps/components/step2/step2-edit-content'
import Step3PrintFormat from '@/views/pu-manage/components/ps/components/step3/step3-print-format'
import DetailListContentForPs from '@/views/pu-manage/components/ps/detail-list'
import StepContentForPs from '@/views/pu-manage/components/ps/step'
import { mapActions, mapState } from 'vuex'
export default {
  name: 'RightContentForPs',
  components: { Step1SelectCover, Step2EditContent, Step3PrintFormat, DetailListContentForPs, StepContentForPs },
  data () {
    return {
      selectedStep: -1,
      saveLoading: false
    }
  },
  props: {
    selectedBookId: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapState({
      currentSelectedBook: state => state.ps.currentSelectedBook,
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedGroupId: state => state.family.currentSelectedGroupId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    })
  },
  watch: {
    currentSelectedBook: {
      handler (val) {
        this.selectedStep = -1
      }
    }
  },
  methods: {
    ...mapActions(['SaveCoverConfig', 'GetBookDetail', 'UpdateStep']),
    handleStep (num) {
      if (num === 4) {
        this.$confirm({
          title: '生成谱书',
          content: '确定要生成谱书吗?',
          okText: '确定',
          cancelText: '取消',
          onOk: async () => {
            this.$message.info('功能暂未实现')
          }
        })
      } else {
        this.selectedStep = num
        this.UpdateStep(num)
      }
    },
    handleReturn () {
      this.selectedStep = -1
    },
    async getDetail () {
      const { GetBookDetail, currentSelectedClanId, selectedBookId } = this
      console.log('🚀 ~ getDetail ~ selectedBookId:', selectedBookId)
      console.log('🚀 ~ getDetail ~ currentSelectedClanId:', currentSelectedClanId)
      if (!currentSelectedClanId || !selectedBookId) {
        return
      }
      this.bookDetailLoading = true
      await GetBookDetail({ clan_id: currentSelectedClanId, book_id: selectedBookId })
      this.bookDetailLoading = false
    },
    async handleSave (params) {
      const { SaveCoverConfig, currentSelectedBook, currentSelectedClanId, saveLoading } = this
      if (saveLoading) {
        return
      }
      if (!currentSelectedBook) {
        this.$message.info('请先选择谱书')
        return
      }
      if (!currentSelectedClanId) {
        this.$message.info('请先选择或新增谱系')
        return
      }
      const newParams = {
        clan_id: currentSelectedClanId,
        book_id: currentSelectedBook.id,
        ...params
      }
      this.saveLoading = true
      const res = await SaveCoverConfig(newParams)
      this.saveLoading = false
      if (res.code === 200) {
        this.$message.success('保存成功')
        this.selectedStep = -1
        await this.getDetail()
      } else {
        this.$message.error(res.message || '保存失败')
      }
    }
  }
}
</script>
<style lang="less" scoped>
.right-content {
  margin-top: 6px;
  height: calc(100vh - 70px);
  overflow-y: auto;
  flex: 1;
  background-color: #fff;
  position: relative;
  margin-left: 10px;
  padding: 30px 0px 0 30px;
}
</style>
