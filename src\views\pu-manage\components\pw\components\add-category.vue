<template>
  <a-modal
    title="新建类目"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    :width="600"
    :footer="null"
    @cancel="cancelHandel"
  >
    <div>
      <div>系统类目</div>
      <a-spin :spinning="loading">
        <div class="list mt8">
          <div v-for="item in sysClanList" :key="item.id" class="item">
            <div
              class="edit-input"
              @click="handleClick(item)"
              :class="selectedCategory.id === item.id ? 'on':''"
            >{{ item.name }}</div>
          </div>
        </div>
      </a-spin>
      <div><a-checkbox class="mr6" @change="handleCheckbox"/>自定义类目</div>
      <a-form :form="form" :label-col="{ span: 4 }" :wrapper-col="{ span: 19 }" @submit="handleSubmit">
        <a-form-item label="类目名称" class="form-wrap" v-if="custom">
          <a-input
            placeholder="请输入类目名称"
            maxLength="50"
            v-decorator="['name', {rules: [{ required: true, message: '请输入类目名称' }]}]"
          />
        </a-form-item>
        <a-form-item :wrapper-col="{ span: 12, offset: 8 }">
          <a-button type="default" class="mr10" @click="$emit('close')">取消</a-button>
          <a-button type="primary" :loading="confirmLoading" html-type="submit">确定</a-button>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>
<script>
import { mapActions, mapState } from 'vuex'
export default {
  name: 'AddCategory',
  data () {
    return {
      visible: true,
      sysClanList: [],
      custom: false,
      loading: false,
      confirmLoading: false,
      selectedCategory: {},
      form: this.$form.createForm(this)
    }
  },
  props: {
    editItem: {
      type: Object,
      default: null
    }
  },
  computed: {
    ...mapState({
      currentSelectedClanId: state => state.family.currentSelectedClanId
    })
  },
  async mounted () {
    this.loading = true
    const res = await this.GetCategoryList({ clanId: this.currentSelectedClanId })
    this.loading = false
    if (res.code === 200) {
      this.sysClanList = res.data.List.filter(item => item.is_system === 1)
    }
  },
  methods: {
    ...mapActions(['GetCategoryList', 'AddCategory']),
    handleClick (item) {
      this.selectedCategory = item
    },
    handleAdd () {
      this.visible = true
    },
    cancelHandel () {
      this.visible = false
      this.$emit('close')
    },
    handleCheckbox (e) {
      this.custom = e.target.checked
    },
    handleSubmit (e) {
      e.preventDefault()
      const { form: { validateFields }, AddCategory } = this
      const params = {
        clan_id: this.currentSelectedClanId
      }
      validateFields(async (errors, values) => {
       if (!errors) {
         console.log('values', values)
         if (this.custom) {
           params.name = values.name
           params.type = 0
         } else {
           if (!this.selectedCategory.id) {
             this.$message.error('请选择类目')
             return
           }
           params.name = this.selectedCategory.name
           params.type = this.selectedCategory.is_system
         }
         this.confirmLoading = true
         const result = await AddCategory(params)
         this.confirmLoading = false
         if (result.code === 200) {
           this.$emit('refresh')
           this.$emit('close')
         }
       }
      })
    }
  }
}
</script>
<style lang='less' scoped>
.list {
  display: flex;
  flex-wrap: wrap;
  .item {
    margin-right: 10px;
    margin-bottom: 10px;
    .edit-input {
      display: inline-block;
      padding: 0 10px;
      min-width: 60px;
      height: 26px;
      line-height: 26px;
      box-sizing: border-box;
      background-color: #ebeef5;
      border-radius: 4px;
      text-align: center;
      cursor: pointer;
      color: grey;
    }
    .edit-input.on{
      background-color: #fdf1e7;
      color: #f86e04;
      box-shadow: inset 0 0 0 1px #f86e04;
    }
  }
}
.form-wrap {
  /deep/ .ant-form-item-label {
    text-align:left;
  }
}
</style>
