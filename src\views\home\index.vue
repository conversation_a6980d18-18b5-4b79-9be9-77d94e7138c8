<template>
  <a-spin size="large" :spinning="loadPageData">
    <div class="flex flex-direction-column ml6">
      <div class="head">
        <div class="flex">
          <!-- 修改 v-model 和选项列表 -->
          <a-select v-model="selectedGroupId" style="width: 140px">
            <a-select-option value="all">全部分组</a-select-option>
            <a-select-option v-for="item in groupList" :key="item.id" :value="item.id">{{ item.group_name
            }}</a-select-option>
          </a-select>
          <!--          <a-input-search placeholder="输入要查找的族员名字" style="width:300px; margin-left: 24px;">-->
          <!--            <a-button slot="enterButton">-->
          <!--              <div class="flex align-center primary">-->
          <!--                <i class="cus-edit-toolbar-icon icon-edit-toolbarfangdajing" />-->
          <!--                搜索-->
          <!--              </div>-->
          <!--            </a-button>-->
          <!--          </a-input-search>-->
          <span class="ml12 flex flex-direction-row align-center c-pointer-hover" @click="showGroupModal = true">
            <i class="cus-edit-toolbar-icon icon-edit-toolbaryemian" /> 分组管理
          </span>
        </div>
        <div class="flex-1 flex flex-direction-row justify-content-end">
          <a-popover placement="bottom">
            <template slot="content">
              <div class="mt4" style="width: 230px">当前管理家族数量：1</div>
              <div class="mt6">当管理家族数量上限：1</div>
              <div class="flex bg-primary justify-content-space-between mt10" style="padding: 4px 12px">
                <div>专项版: 1</div>
                <div>额外: 0</div>
              </div>
            </template>
            <div class="flex flex-direction-row align-center family-block">
              <span class="mr4">家族管理</span>
              <div class="flex f16" style="width:140px">
                <a-progress :stroke-color="{ '0%': '#f98b36', '100%': '#f98b36' }" :percent="100">
                  <template #format="percent">
                    <span class="primary">1</span>/1
                  </template>
                </a-progress>
              </div>
              <!--              <a-button class="primary bd-primary" size="small">扩容</a-button>-->
            </div>
          </a-popover>
          <div class="ml24">协管: <span style="color: #b9a012">0</span></div>
          <div class="ml24 mr16">协作: <span style="color: #51b912">0</span></div>
        </div>
      </div>
      <div class="main">
        <!-- 修改循环使用的 groupList 为 filteredGroupList -->
        <div class="section" v-for="item in filteredGroupList" :key="item.id">
          <div class="title">
            <div class="left">
              <span>{{ item.group_name || '' }}</span>
              <i
                title="编辑"
                class="cus-edit-toolbar-icon icon-edit-toolbarxiugai c-999 c-pointer-hover"
                style="font-size:16px;" />
            </div>
            <div class="right">
              <div class="item">
                <i
                  class="cus-edit-toolbar-icon icon-edit-toolbarjia c-pointer-hover"
                  style="font-size: 14px"
                  @click="handleAdd(item)" />
                <span class="ml6 txt" @click="handleAdd(item)">创建家族/家谱</span>
              </div>
              <div class="item c-pointer-hover">
                <i
                  class="cus-edit-toolbar-icon icon-edit-toolbarpaixu c-pointer-hover"
                  style="font-size: 14px"
                  @click="handleFamilySort" />
                <span class="ml6 txt" @click="handleFamilySort(item)">家族排序</span>
              </div>
            </div>
          </div>
          <div class="list">
            <div
              class="item"
              :class="'tag-guanli'"
              v-for="project in item.clan_list"
              :key="project.id"
              @click="handleClainSelected(item, project)">
              <div class="inner">
                <div class="" style="width: 158px; height: 205px;">
                  <div
                    class="cover-container"
                    style="transform: scale(1.58);"
                    @click="handleClainSelected(item, project, 'px')">
                    <img data-v-0818750d="" src="../../assets/cover-list2.png" alt="" class="cover" />
                    <div class="title">
                      <div class="title2">
                        <div class="title3">
                          <div class="title4" style="transform: scale(0.56);">{{ project.name || '' }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="title-name">
                  <div class="txt line-word">{{ project.name || '' }}</div>
                  <i
                    class="cus-edit-toolbar-icon icon-edit-toolbarxiugai c-pointer-hover"
                    style="font-size:18px"
                    @click="handleEdit(item, project)" />
                </div>
              </div>
              <div class="btn-v align-center">
                <div class="btn c-pointer-hover" @click="handleFamilyBaseInfo(item, project)">家族信息</div>
                <a-popover placement="rightBottom">
                  <template slot="content">
                    <div>更多设置</div>
                    <div class="ml6 more-opt-btn-item" @click="handleClainSelected(item, project, 'px')">
                      <i class="cus-edit-toolbar-icon icon-edit-toolbarnaotu mr6" style="font-size:16px" />录入谱系
                    </div>
                    <div class="ml6 more-opt-btn-item" @click="handleClainSelected(item, project, 'pw')">
                      <i
                        class="cus-edit-toolbar-icon icon-edit-toolbarzengjiawenzhang  mr6"
                        style="font-size:16px" />编辑谱文
                    </div>
                    <div class="ml6 more-opt-btn-item" @click="handleClainSelected(item, project, 'ps')">
                      <i class="cus-edit-toolbar-icon icon-edit-toolbarshuben  mr6" style="font-size:16px" />制作谱书
                    </div>
                    <div class="ml6 more-opt-btn-item" style="display:none">
                      <i class="cus-edit-toolbar-icon icon-edit-toolbarmigrate  mr6" style="font-size:16px" />迁移分组
                    </div>
                    <div class="ml6 more-opt-btn-item" @click="handleDelete(item)">
                      删除
                    </div>
                  </template>
                  <div class="btn c-pointer-hover">更多设置</div>
                </a-popover>
              </div>
            </div>
            <div class="item">
              <div class="add" @click="handleAdd(item)">
                <i class="cus-edit-toolbar-icon icon-edit-toolbarjia2 primary" style="font-size: 30px" />
                <div class="add-txt">创建家族/家谱</div>
              </div>
            </div>
          </div>
        </div>
        <div class="empty" v-if="groupList.length === 0"> <a-empty /></div>
      </div>
      <family-edit
        v-if="showEditFamilyModal"
        :type="familyType"
        :id="familyEditId"
        :groupId="familyGroupId"
        @close="handleCloseEditModal"
        @refresh="refresh" />
      <family-sort v-if="showFamilySort" :groupId="familyGroupId" @close="handleCloseFamilySort" @refresh="refresh" />
      <family-base v-if="showFamilyBaseInfo" @close="handleFamilyBaseInfo" />
      <add-group v-if="showGroupModal" @close-modal="showGroupModal = false" />
    </div>
  </a-spin>
</template>
<script>
import { mapActions, mapState } from 'vuex'
import cloneDeep from 'lodash.clonedeep'
import FamilyEdit from '@/views/components/family-edit'
import FamilySort from '@/views/components/family-sort'
import FamilyBase from '@/views/components/family-info'
import HomeData from './mock-data.json'
import AddGroup from '@/views/home/<USER>/AddGroup'

export default {
  name: 'Home',
  components: { AddGroup, FamilyEdit, FamilySort, FamilyBase },
  data () {
    return {
      loadPageData: false,
      showGroupModal: false,
      showFamilyBaseInfo: false,
      showFamilySort: false,
      familyType: '',
      familyEditId: 0,
      familyGroupId: '',
      showEditFamilyModal: false,
      dataList: HomeData.data,
      groupList: [],
      selectedGroupId: 'all' // 新增字段，用于记录当前选中的分组ID
    }
  },
  computed: {
    ...mapState({
      groupClan: state => state.family.groupClan,
      hasLoadedGroup: state => state.family.hasLoadedGroup
    }),
    // 新增计算属性，根据 selectedGroupId 筛选 groupList
    filteredGroupList () {
      if (this.selectedGroupId === 'all') {
        return this.groupList
      } else {
        const selectedGroup = this.groupList.find(group => group.id === this.selectedGroupId)
        if (selectedGroup && selectedGroup.children) {
          return [selectedGroup, ...selectedGroup.children]
        } else {
          return [selectedGroup]
        }
      }
    }
  },
  watch: {
    groupClan: {
      handler (val, oldVal) {
        this.groupList = cloneDeep(val).sort((a, b) => a.sort_order - b.sort_order)
      },
      immediate: true // 立即执行，确保组件初始化时能获取到store中的数据
    }
  },
  async mounted () {
    await this.refresh()
  },
  methods: {
    ...mapActions(['GroupClanList', 'SetCurrentGroupId', 'SetCurrentClanId']),
    async refresh () {
      this.loadPageData = true
      // 只有在未加载过数据时才调用API，避免重复请求
      await this.GroupClanList()
      this.loadPageData = false
    },
    handleClainSelected (group, clan, type) {
      this.SetCurrentGroupId(group.id)
      this.SetCurrentClanId(clan.id)
      if (type === 'px') {
        this.$router.push({ path: '/px' })
      } else if (type === 'pw') {
        this.$router.push({ path: '/pw' })
      } else if (type === 'ps') {
        this.$router.push({ path: '/ps' })
      }
    },
    handleFamilyBaseInfo (group, clan) {
      console.log('g', group)
      console.log('c', clan)
      // this.SetCurrentGroupId(group.id)
      this.showFamilyBaseInfo = !this.showFamilyBaseInfo
    },
    handleFamilySort (item) {
      this.familyGroupId = item.id
      this.showFamilySort = !this.showFamilySort
    },
    handleCloseFamilySort () {
      this.familyGroupId = ''
      this.showFamilySort = !this.showFamilySort
    },
    handleAdd (item) {
      this.familyGroupId = item.id
      this.familyType = 'add'
      this.familyEditId = 0
      this.showEditFamilyModal = true
    },
    handleCloseEditModal () {
      this.showEditFamilyModal = false
    },
    handleEdit (group, clan) {
      this.familyGroupId = group.id
      this.familyType = 'edit'
      this.familyEditId = clan.id
      this.showEditFamilyModal = true
    },
    handleDelete (item) {
      this.$confirm({
        title: '',
        content: `确定要删除家族「${item.group_name}」吗?`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          this.$message.success('暂未实现')
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
@import '~@/assets/theme.less';

.head {
  background: #fff;
  height: 60px;
  padding: 10px 20px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #666;
  font-size: 16px;
}

.family-block {
  padding: 2px 8px;
}

.family-block:hover {
  background-color: @bg-background-color;
}

.main {
  margin: 10px;

  .section {
    background-color: #fff;
    padding: 0 30px;

    .title {
      padding: 30px 0;
      display: flex;
      align-items: center;
      font-size: 16px;

      .left {
        position: relative;
        padding-left: 14px;
        font-size: 20px;
        color: #33271f;
        display: inline-flex;
        align-items: center;

        .txt {}
      }

      .left:before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 22px;
        background-color: #f76d02;
      }

      .right {
        display: inline-flex;
        align-items: center;
        margin-left: 50px;

        .item {
          margin-right: 30px;
          cursor: pointer;

          .txt {
            color: #666;
          }

          .txt:hover {
            color: @primary-color;
          }
        }
      }
    }

    .list {
      display: flex;
      flex-wrap: wrap;

      .item {
        position: relative;
        z-index: 0;
        width: 200px;
        min-height: 300px;
        margin: 0 2% 30px 0;
        box-shadow: 0 0 0 1px #ccc;
        transition: all .1s;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        cursor: pointer;
        overflow: hidden;

        .add {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          margin: 10px;
          border: 1px dashed #ccc;

          .add-txt {
            margin-top: 20px;
            font-size: 18px;
          }
        }
      }

      .item:hover {
        box-shadow: 0 0 0 1px #f86e04;
      }

      .item:before {
        position: absolute;
        left: 0;
        top: 0;
        width: 100px;
        height: 30px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        z-index: 10;
        transform: rotate(-45deg);
        transform-origin: 50% 169%;
      }

      .tag-guanli {
        &:before {
          content: "管理";
          background-color: #f86e04;
        }
      }

      .tag-fanli {
        &:before {
          content: "范例";
          background-color: #b91212;
        }
      }

      .inner {
        padding: 10px;
        margin: 10px 10px 0;
        background-color: #f5f5f5;
        box-sizing: border-box;

        .title-name {
          display: flex;
          align-items: center;
          font-size: 18px;
          margin-top: 10px;

          i {
            display: none
          }

          &:hover {
            i {
              display: block;
            }
          }
        }
      }

      .btn-v {
        display: flex;

        .btn {
          width: 100px;
          height: 40px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
      }

      .cover-container {
        width: 100px;
        height: 130px;
        transform-origin: left top;
        position: relative;

        .cover {
          display: block;
          width: 100%;
          height: 100%;
          -o-object-fit: fill;
          object-fit: fill;
        }

        .title {
          position: absolute;
          left: 64px;
          top: 16px;
          width: 24px;
          height: 76px;
          box-sizing: border-box;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: center;
          overflow: hidden;
          border: 2px solid #fff;
          background-color: #92958d;
          padding: 1px;

          .title2 {
            width: 100%;
            height: 100%;
            border: 1px solid #eae2c9;
            background-color: #dab272;
            box-sizing: border-box;
            padding: 1px;

            .title3 {
              width: 100%;
              height: 100%;
              background-color: #fff6c3;
              display: flex;
              flex-direction: column;
              justify-content: center;

              .title4 {
                width: 100%;
                writing-mode: vertical-lr;
                white-space: nowrap;
                text-align: center;
                font-size: 12px;
                line-height: 14px;
                box-sizing: border-box;
              }
            }
          }
        }
      }
    }
  }

  .section:not(:last-child) {
    margin-bottom: 10px;
  }
}

.more-opt-btn-item {
  padding: 2px 8px;
}

.more-opt-btn-item:hover {
  background-color: @bg-background-color;
  cursor: pointer;
}
</style>
