<template>
  <a-modal
    title="新建谱文"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    :width="600"
    :footer="null"
    @cancel="cancelHandel"
  >
    <a-form :form="form" :label-col="{ span: 4 }" :wrapper-col="{ span: 19 }" @submit="handleSubmit">
      <a-form-item label="谱文标题" class="form-wrap">
        <a-input
          placeholder="请输入谱文标题"
          :suffix="len+'/100'"
          :maxLength="100"
          @change="handleChange"
          v-decorator="['name', {
            rules: [{ required: true, message: '请输入谱文标题' }],
            normalize: (value) => value && value.trim()
          }]"
        />
      </a-form-item>
      <a-form-item label="谱文布局" class="form-wrap">
        <a-select
          placeholder="请选择谱文布局"
          v-decorator="['layout', {rules: [{ required: true, message: '请选择谱文布局' }]}]"
        >
          <a-select-option value="横向左开">横向左开</a-select-option>
          <a-select-option value="竖向右开">竖向右开</a-select-option>
          <a-select-option value="竖向左开">竖向左开</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="纸张" class="form-wrap">
        <a-select
          placeholder="请选择纸张"
          v-decorator="['print_id', {rules: [{ required: true, message: '请选择纸张' }]}]"
        >
          <a-select-option :value="1">默认纸张</a-select-option>
          <a-select-option :value="2">横向纸张</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="类目名称" class="form-wrap" v-if="!category">
        <a-select
          placeholder="请选择类目名称"
          v-decorator="['category_id', {rules: [{ required: false, message: '请选择类目名称' }]}]"
        >
          <a-select-option :value="item.id" v-for="item in sysClanList" :key="item.id">{{ item.name }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item :wrapper-col="{ span: 12, offset: 8 }">
        <a-button type="default" class="mr10" @click="$emit('close')">取消</a-button>
        <a-button type="primary" :loading="confirmLoading" html-type="submit">确定</a-button>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
import { mapState, mapActions } from 'vuex'
export default {
  name: 'AddArticle',
  data () {
    return {
      visible: true,
      sysClanList: [],
      loading: false,
      len: 0,
      confirmLoading: false,
      selectedCategory: {},
      form: this.$form.createForm(this)
    }
  },
  computed: {
    ...mapState({
      currentSelectedClanId: state => state.family.currentSelectedClanId
    })
  },
  props: {
    category: {
      type: Object,
      default: null
    }
  },
  async mounted () {
    this.loading = true
    const res = await this.GetCategoryList({ clanId: this.currentSelectedClanId })
    this.loading = false
    if (res.code === 200) {
      this.sysClanList = res.data.List.filter(item => item.is_system === 1)
    }
  },
  methods: {
    ...mapActions(['AddClanArticle', 'GetCategoryList']),
    handleChange (e) {
      this.len = e.target.value.length
    },
    handleSubmit (e) {
      e.preventDefault()
      const { form: { validateFields }, AddClanArticle } = this
      validateFields(async (errors, values) => {
        if (!errors) {
          const params = {
            clan_id: this.currentSelectedClanId,
            ...values
          }
          if (params.category_id) {
            const category = this.sysClanList.find(item => item.id === params.category_id)
            if (category) {
              params.category_name = category.name
            }
          }
          if (this.category) {
            params.category_name = this.category.name
            params.category_id = this.category.id
          }
          this.confirmLoading = true
          const result = await AddClanArticle(params)
          this.confirmLoading = false
          if (result.code === 200) {
            this.$emit('refresh')
            this.$emit('close')
          }
        }
      })
    },
    cancelHandel () {
      this.visible = false
      this.$emit('close')
    }
  }
}
</script>
<style lang="less" scoped>
</style>
