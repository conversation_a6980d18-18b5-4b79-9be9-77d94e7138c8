import request from '@/utils/request'

/**
 * @typedef {object} JDAreaItem
 * @property {number} [id] - Fid
 * @property {string} name - 区域名
 * @property {number} areaCode - 区域码，非必返回字段，有就返回
 */

const commonApi = {
    getAreaList: '/web/site/get-area-list'
}

/**
 * 获取省市区数据
 * @param {object} parameter - 请求体
 * @param {Array<JDAreaItem>} parameter.list - 分组列表
 * @returns {Promise<object>}
 */

export async function getAreaList (parameter) {
    return request({
        url: commonApi.getAreaList,
        method: 'get',
        params: parameter
    })
}
