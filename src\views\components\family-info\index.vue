<template>
  <a-drawer
    title="家族信息"
    placement="right"
    :visible="visible"
    width="85vw"
    :after-visible-change="afterVisibleChange"
    @close="onClose"
  >
    <div class="flex">
      <div class="left" style="width:35vw">
        <base-head :title="'家族名称：'+clan?.name" is-edit/>
        <base-item label="编号:" txt="未设置" is-edit />
        <base-item label="家族姓氏:" :txt="clan?.surname" />
        <base-item label="始迁祖:" :txt="clan?.ancestor" />
        <base-item label="堂号:" :txt="clan?.hall_name" />
        <base-item label="所在地区:" :txt="getCity()" />
        <base-item label="家族简介:" :txt="clan?.description" />
      </div>
      <div class="right flex-1">
        <div class="top flex flex-direction-column">
          <base-head title="管理员信息" />
          <div class="flex flex-direction-row align-center user-info">
            <img src="../../../assets/photo.png" class="avatar"/>
            <div class="nickname">150***590</div>
            <div class="account">150********590(466784) <i class="cus-edit-toolbar-icon icon-edit-toolbarfuzhi c-pointer-hover" style="font-size:12px;" /> </div>
          </div>
        </div>
        <div class="qr">
          <base-head title="手机查看家族"/>
        </div>
      </div>
    </div>
    <div>
      <div class="flex flex-direction-row align-center">
        <base-head title="数据统计 - 以下数据为演示数据" />
        <i class="cus-edit-toolbar-icon icon-edit-toolbarwenhao ml4" style="font-size:16px" />
      </div>
      <div class="flex flex-direction-row flex-wrap">
        <statistic-item />
      </div>
    </div>
  </a-drawer>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import BaseHead from '@/views/components/base-head'
import BaseItem from '@/views/components/base-item'
import StatisticItem from './components/index'
  export default {
    name: 'FamilyInfoComponent',
    components: {
      StatisticItem,
      BaseHead,
      BaseItem
    },
    data () {
      return {
        visible: true,
        clan: {}
      }
    },
    computed: {
      ...mapState({
        currentSelectedGroupId: state => state.family.currentSelectedGroupId
      })
    },
    async mounted () {
      if (!this.currentSelectedGroupId) {
        return
      }
      const result = await this.GetCurrentClan()
      console.log('result', result)
      this.clan = result
    },
    methods: {
      ...mapActions(['GetCurrentClan']),
      afterVisibleChange (visible) {
        console.log(visible)
      },
      onClose () {
        // this.visible = false
        this.$emit('close')
      },
      getCity () {
        const { clan } = this
        if (clan) {
          const { province, city, district } = clan
          return `${province}${city}${district}`
        }
        return ``
      }
    }
  }
</script>
<style lang="less">
.user-info{
  height: 48px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  border-radius: 24px 0 0 24px;
  .avatar{
    width: 56px;
    height: 56px;
    border-radius: 100%;
    -o-object-fit: cover;
    object-fit: cover;
  }
  .nickname {
    font-size: 20px;
    margin: 0 20px 0 8px;
  }
}
</style>
