<template>
  <div>
    <div class="children-info" v-if="wifeList.length > 0">
      <span class="line"/>配偶信息({{ wifeList.length }})
    </div>
    <div class="children-list-wrap" v-if="wifeList.length > 0">
      <div class="child-item" v-for="(wife) in wifeList" :key="wife.id">
        <div class="slt">
          <a-select v-model="wife.relation" size="small" style="width:50px" :dropdownMatchSelectWidth="false" @change="handleWifeRelationChange(wife)">
            <a-select-option v-for="item in wifeOptionList" :key="item">{{ item }}</a-select-option>
          </a-select>
        </div>
        <div class="name">{{ wife.name }}</div>
        <!-- <div class="opt-icon">
          <i class="cus-edit-toolbar-icon icon-edit-toolbarkongxinjiantou c-pointer" :class="index === 0 ? 'first-icon':''" @click="handleMove('down','wife', wife, index === 0)"/>
          <i class="cus-edit-toolbar-icon icon-edit-toolbarkongxinjiantou1 c-pointer" :class="index === wifeList.length - 1 ? 'last-icon':''" @click="handleMove('up', 'wife', wife, wifeList.length - 1)"/>
        </div> -->
      </div>
    </div>
    <div class="children-info" v-if="childrenList.length > 0">
      <span class="line"/>子女信息({{ childrenList.length }})
    </div>
    <div class="children-list-wrap" v-if="childrenList.length > 0">
      <div class="child-item" v-for="(child) in childrenList" :key="child.id">
        <div class="slt">
          <a-select size="small" v-model="child.ranking" style="width:50px" :dropdownMatchSelectWidth="false" @change="handleChildRankingChange(child)">
            <a-select-option v-for="item in childrenOptionList" :key="item" :value="item">{{ showName(item, child.sex) }}</a-select-option>
            <a-select-option :value="child.sex === '女' ? '之女' : '之子'">{{ child.sex === '女' ? '之女' : '之子' }}</a-select-option>
          </a-select>
        </div>
        <div class="name">{{ child.name }}</div>
        <!-- <div class="opt-icon">
          <i class="cus-edit-toolbar-icon icon-edit-toolbarkongxinjiantou c-pointer" :class="index === 0 ? 'first-icon':''" @click="handleMove('down', 'child', child, index === 0)"/>
          <i class="cus-edit-toolbar-icon icon-edit-toolbarkongxinjiantou1 c-pointer" :class="index === childrenList.length - 1 ? 'last-icon':''" @click="handleMove('up', 'child', child, index === childrenList.length - 1)"/>
        </div> -->
      </div>
    </div>
  </div>
</template>
<script>
import cloneDeep from 'lodash.clonedeep'
import { getRankText } from '@/utils/rankTextUtil'
import { wifeConstantList } from '@/constant/wife-constant'
import { mapState, mapActions } from 'vuex'
export default {
  name: 'WifeChildrenInfo',
  data () {
    return {
      wifeList: [],
      childrenList: [],
      childrenOptionList: Array(20).fill(0).map((_, index) => index + 1),
      wifeOptionList: wifeConstantList
    }
  },
  props: {
    selectedNode: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    ...mapState({
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    })
  },
  watch: {
    selectedNode: {
      handler (val) {
        if (val) {
          this.childrenList = []
          this.wifeList = []
          if (val.children) {
            this.childrenList = cloneDeep(val.children)
          }
          if (val.wife) {
            this.wifeList = cloneDeep(val.wife)
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    ...mapActions(['SaveMemberInfo']),
    showName (name, sex) {
      return getRankText(name, sex)
    },
    handleMove (direction, type, item, disabled) {
      if (disabled) {
        return
      }
      console.log(direction, type, item)
      this.$message.info('该功能暂未实现')
    },
    async handleWifeRelationChange (wife) {
      // 修改配偶关系
      if (!wife.id) {
        this.$message.error('配偶信息不完整')
        return
      }

      const params = {
        clan_id: this.currentSelectedClanId,
        pedigree_id: this.currentSelectedPedigreeId,
        id: wife.id,
        relation: wife.relation
      }

      try {
        const res = await this.SaveMemberInfo(params)
        if (res.code === 200) {
          this.$message.success('配偶关系修改成功')
          // 通知父组件刷新数据
          this.$emit('refresh')
        } else {
          this.$message.error(res.message || '配偶关系修改失败')
        }
      } catch (error) {
        this.$message.error('配偶关系修改失败')
        console.error('配偶关系修改失败:', error)
      }
    },
    async handleChildRankingChange (child) {
      // 修改子女排行
      if (!child.id) {
        this.$message.error('子女信息不完整')
        return
      }

      // 根据排行数字和性别生成排行文本
      let rankingText = ''
      if (child.ranking === (child.sex === '女' ? '之女' : '之子')) {
        rankingText = child.ranking
      } else {
        rankingText = getRankText(child.ranking, child.sex)
      }

      const params = {
        clan_id: this.currentSelectedClanId,
        pedigree_id: this.currentSelectedPedigreeId,
        id: child.id,
        ranking: child.ranking,
        ranking_text: rankingText
      }

      try {
        const res = await this.SaveMemberInfo(params)
        if (res.code === 200) {
          this.$message.success('子女排行修改成功')
          // 通知父组件刷新数据
          this.$emit('refresh')
        } else {
          this.$message.error(res.message || '子女排行修改失败')
        }
      } catch (error) {
        this.$message.error('子女排行修改失败')
        console.error('子女排行修改失败:', error)
      }
    }
  }
}
</script>
<style  scoped lang="less">
/deep/ .ant-select {
  border: 0;
  font-size:13px;
  outline: none;
  color: #f86e04;
  .ant-select-arrow-icon {
    font-size: 10px !important;
    color: #f86e04;
  }
  .ant-select-selection {
    border: none !important;
    box-shadow: none !important;
  }
  .ant-select-selection__rendered {
    margin-left: 0;
  }
}
.children-info {
  margin-top: 8px;
  display:flex;
  align-content:center;
  align-items:center;
  .line {
    display: inline-block;
    width: 4px;
    height: 16px;
    background: #f76d02;
    margin-right: 6px
  }
}
.children-list-wrap {
  margin-top: 8px;
  .child-item {
    display:flex;
    flex-direction:row;
    align-items: center;
    margin-bottom: 4px;
    .slt{
      font-size: 12px;
      color: #f76d02;
      width:58px; border: 1px dashed #f76d02; padding-left:4px;
    }
    .name{ flex:1; padding-left:4px}
    .opt-icon{width: 50px;}
  }
  .first-icon{
    color: #ccc;
    cursor: not-allowed !important;
  }
  .first-icon:hover{
    color: #ccc;
    cursor: not-allowed !important;
  }
  .last-icon{
    color: #ccc;
    cursor: not-allowed !important;
  }
  .last-icon:hover{
    color: #ccc;
    cursor: not-allowed !important;
  }
}
</style>
