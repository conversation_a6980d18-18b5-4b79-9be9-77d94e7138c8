<template>
  <div>
    <a-modal
      title="添加"
      :visible="visible"
      :width="800"
      :confirmLoading="confirmLoading"
      @ok="handleOk"
      @cancel="handleCancel"
      okText="保存"
      cancelText="取消"
    >
      <div class="tab">
        <div class="item" :class="TagType.分房=== selectedType?'selected':''" @click="handleTabChange(TagType.分房)">分房</div>
        <div class="item" :class="TagType.分屋=== selectedType?'selected':''" @click="handleTabChange(TagType.分屋)">分屋</div>
        <div class="item" :class="TagType.分支=== selectedType?'selected':''" @click="handleTabChange(TagType.分支)">分支</div>
        <div class="item" :class="TagType.分派=== selectedType?'selected':''" @click="handleTabChange(TagType.分派)">分派</div>
      </div>
      <div class="info flex align-center">
        设置 “{{ nodeInfo.name }}” 为 <a-input style="width: 150px;margin-left:4px" v-model="setStr" :placeholder="getInputPlaceholder()" /> 例如：“{{ getExample() }}”
      </div>
      <div class="flex mt4">
        <div>房系描述：</div>
        <div>
          <a-textarea
            v-model="description"
            style="width: 679px"
            :placeholder="getInputPlaceholder(1)"
            :auto-size="{ minRows: 3, maxRows: 5 }"
          />
        </div>
      </div>
      <div class="mt4">
        <a-input-search
          placeholder="请输入要查询人的名称"
          size="large"
          style="width:748px"
          enter-button
          @change="handleSearchTextChange"
          @search="onSearch" />
      </div>
      <div class="mt4">
        <a-table
          :columns="columns"
          :data-source="list"
          :loading="loading"
          size="small"
        >
          <template #action="text, data">
            <span class="c-pointer-hover" @click="handleUpdate(data)">修改</span> |
            <span class="c-pointer-hover" @click="handleDelete(data)">删除</span>
          </template>
        </a-table>
      </div>
    </a-modal>
    <edit-fen-fang-modal
      v-if="showEditFenFangModal"
      :item="nodeInfo"
      :currentData="editFenFangData"
      :tagType="TagType"
      :selectedType="selectedType"
      @close="showEditFenFangModal=false"
      @refresh="handleRefresh"
    />
  </div>
</template>
<script>
import { mapState, mapActions } from 'vuex'
import EditFenFangModal from '@/views/pu-manage/components/px/components/edit-fenfang-modal'
import { TagType } from '@/constant'

const _TagType = TagType
export default {
  name: 'FenFangModal',
  components: { EditFenFangModal },
  data  () {
    return {
      visible: true,
      setStr: '',
      description: '',
      confirmLoading: false,
      TagType: _TagType,
      selectedType: _TagType.分房,
      list: [],
      loading: false,
      columns: [
        {
          title: '人名',
          dataIndex: 'clan_member_name',
          key: 'clan_member_name'
        },
        {
          title: '房系名称',
          dataIndex: 'name',
          key: 'name'
        },
        {
          title: '世系',
          key: 'generation',
          dataIndex: 'generation'
        },
        {
          title: '操作',
          width: '100px',
          key: 'action',
          scopedSlots: { customRender: 'action' }
        }
      ],
      showEditFenFangModal: false,
      editFenFangData: null
    }
  },
  props: {
    nodeInfo: {
      type: Object,
      default: () => {}
    },
    tag: {
      type: Number,
      default: _TagType.分房
    }
  },
  watch: {
    tag: {
      handler (val) {
        this.selectedType = val
      },
      immediate: true,
      deep: true
    },
    nodeInfo: {
      handler (val) {
        this.setStr = val.branch_name
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    ...mapState({
      pedigreeList: state => state.px.list,
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    })
  },
  mounted () {
    this.GetListData()
  },
  methods: {
    ...mapActions(['GetTagTypeList', 'SaveMemberBranch', 'DeletedBranch', 'RefreshPedigreeMemberList']),
    getExample () {
      switch (this.selectedType) {
        case _TagType.分房:
          return '长房'
        case _TagType.分屋:
          return '长屋'
        case _TagType.分支:
          return '一支'
        case _TagType.分派:
          return '一派'
        default:
          return ''
      }
    },
    getInputPlaceholder (type) {
      switch (this.selectedType) {
        case _TagType.分房:
          return `请输入房系${type ? '描述' : '名称'}`
        case _TagType.分屋:
          return `请输入屋系${type ? '描述' : '名称'}`
        case _TagType.分支:
          return `请输入支系${type ? '描述' : '名称'}`
        case _TagType.分派:
          return `请输入派系${type ? '描述' : '名称'}`
        default:
          return ''
      }
    },
    handleTabChange (type) {
      this.selectedType = type
      switch (type) {
        case _TagType.分房:
        this.columns[1].title = '房系名称'
        break
        case _TagType.分屋:
          this.columns[1].title = '屋系名称'
        break
        case _TagType.分支:
          this.columns[1].title = '支系名称'
        break
        case _TagType.分派:
          this.columns[1].title = '派系名称'
        break
      }
      this.GetListData()
    },
    handleChangeGeneration (val) {
      this.generation = val
    },
    async handleOk () {
      const { SaveMemberBranch, RefreshPedigreeMemberList, selectedType, setStr, nodeInfo, description, pedigreeList } = this
      let ancestorName = ''
      if (pedigreeList && pedigreeList.length > 0) {
        ancestorName = pedigreeList[0].name
      }
      const params = {
        clan_id: this.currentSelectedClanId,
        pedigree_id: this.currentSelectedPedigreeId,
        branch_id: 0,
        branch_tag: selectedType,
        name: setStr,
        clan_member_id: nodeInfo.id,
        clan_member_name: nodeInfo.name,
        description: description,
        ancestor: ancestorName
      }
      this.confirmLoading = true
      const res = await SaveMemberBranch(params)
      this.confirmLoading = false
      if (res.code === 200) {
        this.$message.success('操作成功')
        this.$emit('close')
        try {
          await RefreshPedigreeMemberList()
        } catch (refreshError) {
          console.error('刷新数据失败:', refreshError)
          this.$message.warning('操作成功，但刷新数据失败，请手动刷新页面')
        }
      } else {
        this.$message.error(res.message || '操作失败')
      }
    },
    handleCancel () {
      this.$emit('close')
    },
    async GetListData () {
      const { GetTagTypeList } = this
      const params = {
        clan_id: this.currentSelectedClanId,
        pedigree_id: this.currentSelectedPedigreeId,
        tag_type: this.selectedType
      }
      this.loading = true
      const res = await GetTagTypeList(params)
      this.loading = false
      if (res.code === 200) {
        this.list = res.data.list || []
      }
    },
    async handlePageChange (info) {
      this.pagination = info
      await this.GetListData()
    },
    handleSearchTextChange (e) {
      this.searchText = e.target.value.trim()
    },
    async onSearch () {
      await this.GetListData()
    },
    handleChange (data) {
      this.selectedId = data.id
      this.selectedName = data.full_name
    },
    handleRefresh () {
      this.showEditFenFangModal = false
      this.GetListData()
    },
    handleUpdate (data) {
      this.showEditFenFangModal = true
      this.editFenFangData = data
    },
    async handleDelete (data) {
      const { DeletedBranch } = this
      const that = this
      const params = {
        clan_id: this.currentSelectedClanId,
        pedigree_id: that.currentSelectedPedigreeId,
        branch_id: data.id
      }
      this.$confirm({
        title: '',
        content: `确定删除「${data.name}」吗？`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          const res = await DeletedBranch(params)
          if (res.code === 200) {
            that.$message.success('删除成功')
            that.handleRefresh()
          } else {
            that.$message.error(res.message || '删除失败')
          }
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.tab {
  display:flex;
  height: 45px;
  line-height: 45px;
  background-color: #f5f7fa;
  .item {
    flex-grow: 1;
    flex-shrink: 0;
    text-align: center;
    font-size: 16px;
    cursor: pointer;
    position: relative;
    &:after  {
      content: "";
      display: block;
      position: absolute;
      right: 0;
      top: 12px;
      height: 21px;
      border-right: 1px solid #ccc;
    }
  }
  .item.selected {
    background-color: #f86e04;
    color: #fff;
    &:after {
      border-right: none;
    }
  }
  .item:last-child:after {
    display: none;
  }
}
.info {
  margin: 12px 0;
}
</style>
