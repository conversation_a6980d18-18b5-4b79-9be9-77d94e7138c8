<template>
  <a-modal
    title="修改"
    :visible="visible"
    :width="500"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    okText="保存"
    cancelText="取消"
  >
    <a-form
      ref="form"
      :form="form"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-item label="人名" prop="clan_member_name">
        <a-input disabled placeholder="请输入人名" v-decorator="['clan_member_name', { rules: [{ required: false }] }]" />
      </a-form-item>
      <a-form-item :label="upShowText" prop="ancestor_name">
        <a-input disabled placeholder="" v-decorator="['ancestor_name', { rules: [{ required: false }] }]" />
      </a-form-item>
      <a-form-item :label="currentShowText" prop="name">
        <a-input placeholder="请输入" :maxLength="30" v-decorator="['name', { rules: [{ required: true, message: '请输入' + currentShowText }] }]" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
import { mapState, mapActions } from 'vuex'
export default {
  name: 'EditFenFangModal',
  data () {
    return {
      visible: true,
      confirmLoading: false,
      upShowText: '',
      currentShowText: '',
      labelCol: { lg: { span: 5 }, sm: { span: 5 } },
      wrapperCol: { lg: { span: 19 }, sm: { span: 19 } },
      form: this.$form.createForm(this)
    }
  },
  props: {
    item: {
      type: Object,
      default: () => {}
    },
    selectedType: {
      type: Number,
      default: 0
    },
    tagType: {
      type: Object,
      default: () => {}
    },
    currentData: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    ...mapState({
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    })
  },
  watch: {
    selectedType: {
      handler (val) {
        this.getShowStr()
      },
      immediate: true
    },
    currentData: {
      handler (val) {
        this.form.getFieldDecorator('clan_member_name', { initialValue: val.clan_member_name })
        this.form.getFieldDecorator('ancestor_name', { initialValue: val.ancestor_name })
        this.form.getFieldDecorator('name', { initialValue: val.name })
      },
      immediate: true
    }
  },
  methods: {
    ...mapActions(['SaveMemberBranch', 'RefreshPedigreeMemberList']),
    getShowStr () {
      const { selectedType, tagType } = this
      switch (selectedType) {
        case tagType.分房:
          this.upShowText = '上级始祖'
          this.currentShowText = '房系名称'
        break
        case tagType.分屋:
          this.upShowText = '上级房系'
          this.currentShowText = '屋系名称'
        break
        case tagType.分支:
          this.upShowText = '上级屋系'
          this.currentShowText = '支系名称'
        break
        case tagType.分派:
          this.upShowText = '上级支系'
          this.currentShowText = '派系名称'
        break
      }
    },
    handleOk () {
      this.form.validateFields(async (err, values) => {
        if (!err) {
          const { name } = values
          const { SaveMemberBranch, RefreshPedigreeMemberList, selectedType, currentData } = this
          const params = {
            clan_id: this.currentSelectedClanId,
            pedigree_id: this.currentSelectedPedigreeId,
            branch_id: currentData.id,
            branch_tag: selectedType,
            name: name,
            clan_member_id: currentData.clan_member_id,
            clan_member_name: currentData.clan_member_name,
            ancestor: currentData.ancestor_name
          }
          this.confirmLoading = true
          const res = await SaveMemberBranch(params)
          this.confirmLoading = false
          if (res.code === 200) {
            this.$message.success('操作成功')
            this.$emit('refresh')
            this.$emit('close')
            await RefreshPedigreeMemberList()
          } else {
            this.$message.error(res.message || '操作失败')
          }
        }
      })
    },
    handleCancel () {
      this.$emit('close')
    }
  }
}
</script>
<style scoped lang="less">
</style>
