<template>
  <div class="pu-xi-fen-zhi">
    <LabelItem title="选择家族" width="100" class="mt4 mb8">
      <a-select style="width: 100%" v-model="selectedClanId" placeholder="选择家族" @change="handleSelectedClan">
        <a-select-option :key="clan.id" v-for="clan in clanList" :value="clan.id">{{ clan.name }}</a-select-option>
      </a-select>
    </LabelItem>
    <LabelItem title="选择谱系分支" width="100" class="mt4 mb8">
      <a-select style="width: 100%" v-model="selectedPedigreeId" placeholder="选择谱系分支" @change="handleSelectedPedigree">
        <a-select-option :key=" pedigree.id" v-for=" pedigree in pedigreeList" :value=" pedigree.id">{{ pedigree.name }}</a-select-option>
      </a-select>
    </LabelItem>
    <div class="flex flex-direction-row">
      <LabelItem title="房派">
        <a-select style="width: 100%" v-model="fangPai">
          <a-select-option value="">全部族员</a-select-option>
          <a-select-option v-for="(value, key) in TagType" :value="value" :key="value">{{ key }}</a-select-option>
        </a-select>
      </LabelItem>
      <LabelItem title="世数" class="ml4 mr4">
        <a-select style="width: 100%" v-model="shiShu" :dropdownMatchSelectWidth="false">
          <a-select-option value="">全部世系</a-select-option>
          <a-select-option v-for="item in generationNum" :value="item" :key="item">第{{ item }}世</a-select-option>
        </a-select>
      </LabelItem>
      <a-input-search placeholder="请输入关键字">
        <a-button slot="enterButton">搜索</a-button>
      </a-input-search>
    </div>
    <a-spin :spinning="loading">
      <div class="tree">
        <clan-tree
          :show-line="true"
          :noAdd="true"
          :noWife="true"
          :noShowRank="true"
          :noShowPersonIcon="true"
          :nodes="treeData"
          :default-expanded-keys="expandedKeys"
          :defaultCheckedKeys.sync="expandedKeys"
          :selected-key="currentSelectedKey"
          @click="handleSelectedNode"
        />
      </div>
    </a-spin>
  </div>
</template>
<script>
import { mapState } from 'vuex'
 import LabelItem from '@/components/LabelItem'
 import { TagType } from '@/constant'
import { getPedigreeList, getPedigreeMemberList } from '@/api/px'
import { transformData } from '@/utils/transformData'
import ClanTree from '@/components/clan-tree-select/Tree'
import { getSpecificLevelIdList } from '@/utils/getLevelIdsUtls'
 export default {
  name: 'RightPuXiFenZhi',
   components: { LabelItem, ClanTree },
   data () {
    return {
      treeData: [],
      expandedKeys: [],
      currentSelectedKey: '',
      fangPai: '',
      shiShu: '',
      TagType,
      clanList: [],
      loading: false,
      pedigreeList: [],
      selectedClanId: '',
      selectedPedigreeId: '',
      generationNum: Array(200).fill(0).map((_, index) => index + 1)
    }
   },
   computed: {
     ...mapState({
       groupClan: state => state.family.groupClan,
       currentSelectedClanId: state => state.family.currentSelectedClanId
     })
   },
   watch: {
     groupClan: {
        handler (val) {
          let list = []
            if (val?.length > 0) {
              (val || []).forEach(item => {
                if (item.clan_list?.length > 0) {
                  list = list.concat(item.clan_list)
                }
              })
            }
            this.clanList = list
       },
       deep: true,
       immediate: true
     },
     currentSelectedClanId: {
       handler (val) {
         this.selectedClanId = val
       },
       deep: true,
       immediate: true
     },
     selectedClanId: {
      handler (val) {
        this.selectedPedigreeId = undefined
        this.handleGetPedigreeList()
      },
      deep: true,
      immediate: true
    },
     selectedPedigreeId: {
       handler (val) {
         this.treeData = []
         if (val) {
           this.getTreeData()
         }
       }
     }
   },
    methods: {
     handleSelectedClan (id) {
       this.selectedClanId = id
     },
     handleSelectedPedigree (id) {
       this.selectedPedigreeId = id
     },
      handleSelectedNode () {},
     async handleGetPedigreeList () {
        const res = await getPedigreeList({ clan_id: this.selectedClanId })
        if (res?.code === 200) {
           this.pedigreeList = res?.data?.list || []
            if (this.pedigreeList.length === 1) {
               this.selectedPedigreeId = this.pedigreeList[0].id
            }
        }
     },
     async getTreeData () {
       const { selectedClanId, selectedPedigreeId } = this
        const params = {
          clan_id: selectedClanId,
          pedigree_id: selectedPedigreeId,
          generation: 3
        }
       this.loading = true
       const res = await getPedigreeMemberList(params)
       this.loading = false
       if (res?.code === 200) {
          const data = [res?.data]
          transformData(data)
          const idList = getSpecificLevelIdList(data, 1, 1)
          this.expandedKeys = idList
          this.treeData = data
        }
      }
   }
 }
</script>
<style lang='less' scoped>
.pu-xi-fen-zhi {
  height: 100%;
  padding: 10px;
  background-color: #fff;

  .item-form {
    display: flex;
    align-items:center;
    align-content:center;
    border: 1px solid #e0e0e0;
    border-radius:2px;
    .label {
      height: 32px;
      color: #666;
      text-align:center;
      line-height: 32px;
      background-color: #f5f5f5;
    }
    .content {
      flex: 1;
    }
  }
  /deep/ .ant-select-selection{
    border: 0;
  }
}
 </style>
