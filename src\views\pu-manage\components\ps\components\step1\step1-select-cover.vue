<template>
  <div class="step1-select-cover-wrap">
    <div class="title">封面、封底、扉页选择</div>
    <div class="cover-wrap">
      <div class="left">
        <a-tabs :default-active-key="1" @change="handleTabChange">
          <a-tab-pane :key="1" tab="封面设计">
            <div class="item"><label class="mr6">显示封面</label><a-checkbox v-model="saveData.show_cover"/></div>
            <div class="item"><label class="mr16">封面标题</label></div>
            <div class="item"><a-input class="a-input" v-model="saveData.title"/></div>
            <div class="item"><label style="margin-right: 36px">副标题</label><a-checkbox class="mr6" v-model="showFieldsList.sub_title">显示</a-checkbox></div>
            <div class="item"><a-input class="a-input" v-model="saveData.sub_title"/></div>
            <div class="item"><label style="margin-right: 50px">卷号</label><a-checkbox class="mr6" v-model="showFieldsList.volume">显示</a-checkbox></div>
            <div class="item"><a-input class="a-input" v-model="saveData.volume"/></div>
            <div class="item"><label style="margin-right: 50px">堂号</label><a-checkbox class="mr6" v-model="showFieldsList.hall">显示</a-checkbox></div>
            <div class="item"><a-input class="a-input" v-model="saveData.hall"/></div>
            <div class="item"><label style="margin-right: 22px">编修日期</label><a-checkbox class="mr6" v-model="showFieldsList.edit_date">显示</a-checkbox></div>
            <div class="item"><a-input class="a-input" v-model="saveData.edit_date"/></div>
          </a-tab-pane>
          <a-tab-pane :key="2" tab="扉页设计">
            <div class="item"><label class="mr6">显示扉页</label><a-checkbox v-model="saveData.show_inner_page"/></div>
            <div class="item"><label class="mr6">扉页标题</label></div>
            <div class="item"><a-input class="a-input" v-model="saveData.inner_title"/></div>
            <div class="item"><label style="margin-right: 36px">副标题</label><a-checkbox class="mr6" v-model="showFieldsList.inner_sub_title">显示</a-checkbox></div>
            <div class="item"><a-input class="a-input" v-model="saveData.inner_sub_title"/></div>
            <div class="item"><label style="margin-right: 50px">卷号</label><a-checkbox class="mr6" v-model="showFieldsList.inner_volume">显示</a-checkbox></div>
            <div class="item"><a-input class="a-input" v-model="saveData.inner_volume"/></div>
            <div class="item"><label style="margin-right: 50px">堂号</label><a-checkbox class="mr6" v-model="showFieldsList.inner_hall">显示</a-checkbox></div>
            <div class="item"><a-input class="a-input" v-model="saveData.inner_hall"/></div>
            <div class="item"><label style="margin-right: 22px">编修日期</label><a-checkbox class="mr6" v-model="showFieldsList.inner_edit_date">显示</a-checkbox></div>
            <div class="item"><a-input class="a-input" v-model="saveData.inner_edit_date"/></div>
            <div class="item"><label style="margin-right: 50px">主编</label><a-checkbox class="mr6" v-model="showFieldsList.editor">显示</a-checkbox></div>
            <div class="item"><a-input class="a-input" v-model="saveData.editor"/></div>
            <div class="item"><label style="margin-right: 22px">责任编辑</label><a-checkbox class="mr6" v-model="showFieldsList.main_editor">显示</a-checkbox></div>
            <div class="item"><a-input class="a-input" v-model="saveData.main_editor" /></div>
          </a-tab-pane>
        </a-tabs>
        <div class="mt6 text-center">
          <a-button @click="$emit('return')" class="mr6">返回</a-button>
          <a-button @click="handleSave" type="primary" :loading="$parent.saveLoading">确认</a-button>
        </div>
      </div>
      <div class="middle">
        <div class="item">
          <label class="mr4">选择{{ selectedTab === 1 ? '封面' : '扉页' }}</label>
          <a-select v-if="selectedTab === 1" style="width: 120px" size="small" v-model="saveData.type">
            <a-select-option :value="item.id" v-for="item in coverTypesList" :key="item.id">{{ item.name }}</a-select-option>
          </a-select>
        </div>
        <div v-if="selectedTab === 1" class="list">
          <div class="item" :class="saveData.type === 1? 'selected':''" @click="saveData.type=1">
            <div class="img">
              <img :src="type1_1" />
            </div>
          </div>
          <div class="item" :class="saveData.type === 2? 'selected':''" @click="saveData.type=2">
            <div class="img">
              <img :src="type101_1" />
            </div>
          </div>
        </div>
        <div v-if="selectedTab === 2" class="list">
          <div class="item" :class="saveData.inner_type === 1? 'selected':''" @click="saveData.inner_type=1">
            <div class="img">
              <img :src="inner1_1" />
            </div>
          </div>
          <div class="item" :class="saveData.inner_type === 2? 'selected':''" @click="saveData.inner_type=2">
            <div class="img">
              <img :src="inner2_1" />
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <cover1-preview v-if="selectedTab === 1 && saveData.type===1" :data="saveData"/>
        <cover2-preview v-if="selectedTab === 1 && saveData.type===2" :data="saveData"/>
        <inner1-preview v-if="selectedTab === 2 && saveData.inner_type===1" :data="saveData"/>
        <inner2-preview v-if="selectedTab === 2 && saveData.inner_type===2" :data="saveData"/>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { coverTypesList } from '@/constant/cover-types'
import type1_1 from '@/assets/prints/type1-1.png'
import type1_2 from '@/assets/prints/type1-2.png'
import type101_1 from '@/assets/prints/type101-1.png'
import type101_2 from '@/assets/prints/type101-2.png'
import inner1_1 from '@/assets/prints/inner1-1.png'
import inner1_2 from '@/assets/prints/inner1-2.png'
import inner2_1 from '@/assets/prints/inner2-1.png'
import inner2_2 from '@/assets/prints/inner2-2.png'
import Cover1Preview from '@/views/pu-manage/components/ps/components/step1/cover1-preview'
import Cover2Preview from '@/views/pu-manage/components/ps/components/step1/cover2-preview'
import Inner1Preview from '@/views/pu-manage/components/ps/components/step1/inner1-preview'
import Inner2Preview from '@/views/pu-manage/components/ps/components/step1/inner2-preview'
export default {
  name: 'Step1SelectCover',
  components: { Inner1Preview, Inner2Preview, Cover2Preview, Cover1Preview },
  data () {
    return {
      type1_1,
      type1_2,
      type101_1,
      type101_2,
      inner1_1,
      inner1_2,
      inner2_1,
      inner2_2,
      coverTypesList,
      selectedTab: 1,
      showFieldsList: {
        sub_title: false,
        volume: false,
        hall: false,
        edit_date: false,
        inner_sub_title: false,
        inner_volume: false,
        inner_hall: false,
        inner_edit_date: false,
        editor: false,
        main_editor: false
      },
      saveData: {
        show_cover: false,
        type: 1,
        title: '',
        sub_title: '',
        volume: '',
        hall: '',
        edit_date: '',
        show_inner_page: false,
        inner_type: 1,
        inner_title: '',
        inner_sub_title: '',
        inner_volume: '',
        inner_hall: '',
        inner_edit_date: '',
        editor: '',
        main_editor: ''
      }
    }
  },
  computed: {
    ...mapState({
      currentSelectedBook: state => state.ps.currentSelectedBook,
      currentSelectedBookDetail: state => state.ps.currentSelectedBookDetail
    })
  },
  watch: {
    currentSelectedBookDetail: {
      handler (val) {
        const { cover_config } = val || {}
        if (cover_config) {
          const { show_list } = cover_config
          for (const valKey in this.saveData) {
            if (valKey === 'show_cover') {
              this.saveData[valKey] = cover_config[valKey] === 1
            } else if (valKey === 'show_inner_page') {
              this.saveData[valKey] = cover_config[valKey] === 1
            } else {
              this.saveData[valKey] = cover_config[valKey]
            }
          }
          try {
            const list = JSON.parse(show_list || '[]')
            list.forEach(item => {
              console.log('item', item)
              this.showFieldsList[item] = true
            })
          } catch (e) {
            console.log(e)
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    handleTabChange (key) {
      this.selectedTab = key
    },
    handleSave () {
      const { showFieldsList } = this
      const showList = []
      for (const str in showFieldsList) {
        if (showFieldsList[str]) {
          showList.push(str)
        }
      }
      const params = {
        ...this.saveData,
        show_list: showList.length > 0 ? JSON.stringify(showList) : ''
      }
      this.$emit('save', params)
    }
  }
}
</script>
<style scoped lang='less'>
.step1-select-cover-wrap {
  background-color: #fff;
  height: 100%;
  padding: 0;
  box-sizing: border-box;
  text-align: left;

  .title {
    font-size: 20px;
    padding: 0 0 20px;
    font-weight: 700;
  }
  .cover-wrap {
    display: flex;
    height: calc(100% - 21px);
    .left {
      width: 260px;
      border-right: 1px solid #eee;
      .item { margin: 4px 0;}
      .a-input {
        width: 250px;
      }
    }
    .middle {
      width: 250px;
      border-right: 1px solid #eee;
      box-sizing: border-box;
      padding: 0 20px;
      overflow-y: auto;
      .list {
        margin-top:12px;
        display: flex;
        flex-wrap: wrap;
        flex-direction:column;
        position:relative;
        .item {
          margin-top: 15px;
          width: 120px;
          position: relative;
          overflow: hidden;
          .img {
            width: 120px;
            height: 170px;
            overflow: hidden;
            position: relative;
            cursor: pointer;
            img {
              max-width: 120px;
              height: auto;
            }
          }
        }
        .item.selected {
          .img {
            &::before {
              content: "";
              display: block;
              position: absolute;
              width: 120px;
              height: 20px;
              left: -60px;
              top: -10px;
              transform: rotate(-45deg) translateY(25px);
              background-color: #51b912;
            }
            &::after {
              content: "当前封面";
              color: rgb(255, 255, 255);
              display: block;
              position: absolute;
              width: 120px;
              left: -60px;
              top: -10px;
              height: 20px;
              line-height: 20px;
              text-align: center;
              font-size: 12px;
              transform: rotate(-45deg) translateY(25px) scale(0.7);
            }
          }
        }
        .item.selected::after {
          content: "";
          display: block;
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 170px;
          box-sizing: border-box;
          cursor: pointer;
          border: 4px solid #51b912;
        }
      }
    }
    .right {
      flex: 1;
      .wrap {
        display: flex;
        flex-wrap: wrap;
        .item {
          width: 380px;
          height: 600px;
          margin-left: 20px;
          .img {
            height: 540px;
            .inner {
              width: 760px;
              height: 1080px;
              position: relative;
              transform: scale(.5);
              transform-origin: 0 0;
              img {
                border: none;
                max-width: 100%;
              }
              .info {
                color: #f4d128;
                .title{
                  position: absolute;
                  left: 533px;
                  top: 65px;
                  width: 146px;
                  height: 743px;
                  text-align: center;
                  font-size: 70px;
                  line-height: 1;
                  display: flex;
                  flex-direction: column;
                  justify-content: space-around;
                }
                .sub-title {
                  position: absolute;
                  left: 650px;
                  top: 85px;
                  width: 20px;
                  height: 743px;
                  text-align: center;
                  font-size: 20px;
                  line-height: 1.3;
                }
                .volume {
                  position: absolute;
                  left: 544px;
                  top: 65px;
                  width: 20px;
                  height: 723px;
                  text-align: center;
                  font-size: 20px;
                  line-height: 1.3;
                  display: flex;
                  flex-direction: column;
                  justify-content: flex-end;
                }
                .space {
                  height: 20px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
