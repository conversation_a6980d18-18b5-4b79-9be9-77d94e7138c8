<template>
  <div class="pushu-page">
    <ps-header />
    <div class="flex">
      <left-content-for-p-s @onChangeSelectedBookId="onChangeSelectedBookId" />
      <right-content-for-ps :selectedBookId="selectedBookId" />
    </div>
  </div>
</template>

<script>
import LeftContentForPS from '@/views/pu-manage/components/ps/left-content'
import RightContentForPs from '@/views/pu-manage/components/ps/right-content'
import PsHeader from '@/views/pu-manage/components/ps/top'
export default {
  name: 'PSPage',
  components: { RightContentForPs, PsHeader, LeftContentForPS },
  data () {
    return {
      selectedBookId: ''
    }
  },
  mounted () {
  },
  methods: {
    onChangeSelectedBookId (val) {
      this.selectedBookId = String(val)
    }
  }
}
</script>
<style lang="less" scoped>
.hard {
  text-align: center;
}

.pushu-page {
  overflow: hidden;
  width: 100%;
  text-align: center;
}

.page {
  background: white;
  text-align: center;

  img {
    visibility: hidden;
  }
}

.turn-page {
  img {
    visibility: visible;
  }
}
</style>
