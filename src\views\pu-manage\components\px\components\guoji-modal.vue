<template>
  <a-modal
    title="过继"
    :visible="visible"
    :width="800"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    okText="保存"
    cancelText="取消"
  >
    <div class="tab">
      <div class="item" :class="TabType.生=== selectedType?'selected':''" @click="handleTabChange(TabType.生)">迁移本人及后代</div>
      <div class="item" :class="TabType.嗣=== selectedType?'selected':''" @click="handleTabChange(TabType.嗣)">过继为嗣(继)子</div>
      <div class="item" :class="TabType.祧=== selectedType?'selected':''" @click="handleTabChange(TabType.祧)">过继为祧子</div>
      <div class="item" :class="TabType.关联=== selectedType?'selected':''" @click="handleTabChange(TabType.关联)">关联显示</div>
    </div>
    <div class="info">
      将 <span class="primary">{{ nodeInfo.full_name }}</span> 过继为 <span class="primary">{{ selectedName }}</span> 的 <span class="primary">{{ getShowName() }}</span>
    </div>
    <div>
      <a-input-group compact>
        <a-select :default-value="-1" style="width: 120px" size="large" @change="handleChangeGeneration">
          <a-select-option :value="-1">全部世系</a-select-option>
          <a-select-option
            v-for="num in maxGenerationNum"
            :key="num"
            :value="num"
          >{{ num }}世</a-select-option>
        </a-select>
        <a-input-search
          placeholder="请输入要查询人的姓名"
          size="large"
          style="width:630px"
          enter-button
          @change="handleSearchTextChange"
          @search="onSearch" />
      </a-input-group>
    </div>
    <div>
      <a-table
        :columns="columns"
        :data-source="list"
        :loading="loading"
        size="small"
        :pagination="pagination"
        @change="handlePageChange"
      >
        <template #radio="text, data">
          <a-radio
            :value="text"
            :checked="selectedId === text"
            @change="() => handleChange(data)"
          />
        </template>
        <template #parent="text">
          <span>{{ getParent(text) }}</span>
        </template>
      </a-table>
    </div>
  </a-modal>
</template>
<script>
import { mapState, mapActions } from 'vuex'
const columns = [
  {
    title: '选择',
    dataIndex: 'id',
    key: 'id',
    scopedSlots: { customRender: 'radio' }
  },
  {
    title: '姓名',
    dataIndex: 'full_name',
    key: 'full_name'
  },
  {
    title: '性别',
    dataIndex: 'sex',
    key: 'sex'
  },
  {
    title: '世代',
    key: 'generation',
    dataIndex: 'generation'
  },
  {
    title: '父母',
    key: 'parents',
    dataIndex: 'parents',
    scopedSlots: { customRender: 'parent' }
  }
]
const _TabType = {
  生: '生',
  嗣: '嗣',
  祧: '祧',
  关联: '关联'
}
export default {
  name: 'GuojiModal',
  data  () {
    return {
      visible: true,
      confirmLoading: false,
      maxGenerationNum: Array(200).fill(0).map((_, index) => index + 1),
      TabType: _TabType,
      selectedType: _TabType.生,
      searchText: '',
      generation: -1,
      selectedId: '',
      selectedName: '选择目标',
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      list: [],
      loading: false,
      columns
    }
  },
  props: {
    nodeInfo: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    ...mapState({
      currentSelectedClanId: state => state.family.currentSelectedClanId,
      currentSelectedPedigreeId: state => state.px.currentSelectedPedigreeId
    })
  },
  mounted () {
    this.GetListData()
  },
  methods: {
    ...mapActions(['GetMemberList', 'MoveMember', 'RefreshPedigreeMemberList']),
    getShowName () {
      const { selectedType } = this
      switch (selectedType) {
        case _TabType.生:
          return '子女'
        case _TabType.嗣:
          return '嗣子'
        case _TabType.祧:
          return '祧子'
        case _TabType.关联:
          return '共同后代'
      }
    },
    getParent (data) {
      let result = ''
      if (data?.father) {
          result = data?.father.full_name
      }
      if (data?.mother) {
        result += ` ${data?.mother.full_name}`
      }
      return result
    },
    handleTabChange (type) {
      this.selectedType = type
    },
    handleChangeGeneration (val) {
      this.generation = val
    },
    handleOk () {
      const { nodeInfo, selectedType, TabType, selectedName, handleSubmit } = this
      if (selectedType === TabType.生) {
        const info = `将「${nodeInfo.full_name}」作为生子出继到「${selectedName}」的后代,将会自动断开与当前父亲的关系,确定要继续吗?`
        this.$confirm({
          title: '',
          content: info,
          okText: '确定',
          okType: 'danger',
          cancelText: '取消',
          onOk () {
            handleSubmit()
          }
        })
      } else {
        handleSubmit()
      }
    },
    async handleSubmit () {
      const { selectedId, MoveMember, RefreshPedigreeMemberList, selectedType, nodeInfo } = this
      if (!selectedId) {
        this.$message.error('请选择出继对象')
        return
      }
      const params = {
        clan_id: this.currentSelectedClanId,
        pedigree_id: this.currentSelectedPedigreeId,
        member_id: nodeInfo.id,
        target_id: selectedId,
        relation: selectedType
      }
      this.confirmLoading = true
      const res = await MoveMember(params)
      this.confirmLoading = false
      if (res.code === 200) {
        this.$message.success('操作成功')
        this.$emit('close')
        await RefreshPedigreeMemberList()
      } else {
        this.$message.error(res.message || '操作失败')
      }
    },
    handleCancel () {
      this.$emit('close')
    },
    async GetListData () {
      const { GetMemberList } = this
      const params = {
        clan_id: this.currentSelectedClanId,
        pedigree_id: this.currentSelectedPedigreeId,
        search_generation: this.generation,
        search_name: this.searchText,
        page: this.pagination.current,
        pageSize: this.pagination.pageSize,
        pagination: true
      }
      this.loading = true
      const res = await GetMemberList(params)
      this.loading = false
      if (res.code === 200) {
        this.list = res.data.list || []
        this.pagination.total = res.data.totalCount || 0
      }
    },
    async handlePageChange (info) {
      this.pagination = info
      await this.GetListData()
    },
    handleSearchTextChange (e) {
      this.searchText = e.target.value.trim()
    },
    async onSearch () {
      await this.GetListData()
    },
    handleChange (data) {
      this.selectedId = data.id
      this.selectedName = data.full_name
    }
  }
}
</script>
<style lang="less" scoped>
.tab {
  display:flex;
  height: 45px;
  line-height: 45px;
  background-color: #f5f7fa;
  .item {
    flex-grow: 1;
    flex-shrink: 0;
    text-align: center;
    font-size: 16px;
    cursor: pointer;
    position: relative;
    &:after  {
      content: "";
      display: block;
      position: absolute;
      right: 0;
      top: 12px;
      height: 21px;
      border-right: 1px solid #ccc;
    }
  }
  .item.selected {
      background-color: #f86e04;
      color: #fff;
      &:after {
        border-right: none;
      }
  }
  .item:last-child:after {
    display: none;
  }
}
.info {
  margin: 12px 0;
}
</style>
